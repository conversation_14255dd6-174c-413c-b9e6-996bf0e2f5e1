<div class="settings-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>Account Settings</mat-card-title>
      <mat-card-subtitle>Manage your account information</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="settingsForm" (ngSubmit)="onSubmit()">
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>First Name</mat-label>
            <input matInput formControlName="firstName">
            <mat-error *ngIf="settingsForm.get('firstName')?.invalid">
              First name is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Last Name</mat-label>
            <input matInput formControlName="lastName">
            <mat-error *ngIf="settingsForm.get('lastName')?.invalid">
              Last name is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Email</mat-label>
            <input matInput formControlName="email" >
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Phone Number</mat-label>
            <input matInput formControlName="phoneNumber">
            <mat-error *ngIf="settingsForm.get('phoneNumber')?.invalid">
              Please enter a valid 10-digit phone number
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Birth Date</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="birthDate">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Bio</mat-label>
          <textarea matInput rows="4" formControlName="bio"
                    placeholder="Tell us about yourself..."></textarea>
          <mat-hint align="end">{{settingsForm.get('bio')?.value?.length || 0}}/500</mat-hint>
        </mat-form-field>

        <!-- Read-only Information -->
        <div class="info-section">
          <p><strong>Account Type:</strong> {{userSettings?.userType}}</p>
          <p><strong>Member Since:</strong> {{userSettings?.createdAt | date}}</p>
          <p><strong>Account Status:</strong> {{userSettings?.status}}</p>
          <p><strong>Verification Status:</strong> 
            {{userSettings?.isVerified ? 'Verified' : 'Not Verified'}}
          </p>
        </div>

        <div class="form-actions">
          <button mat-button type="button" routerLink="/">Cancel</button>
          <button mat-raised-button color="primary" type="submit"
                  [disabled]="settingsForm.invalid || isLoading">
            <mat-icon>save</mat-icon>
            Save Changes
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>