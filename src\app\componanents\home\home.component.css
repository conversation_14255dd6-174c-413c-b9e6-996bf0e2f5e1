/* General Styles */
.home-container {
  font-family: '<PERSON>o', sans-serif;
  color: #333;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

h1, h2, h3 {
  font-weight: 700;
}

.btn-primary {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-primary:hover {
  background-color: #3e8e41;
}

.btn-secondary {
  background-color: transparent;
  color: #4CAF50;
  border: 2px solid #4CAF50;
  padding: 10px 22px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
  margin-left: 15px;
}

.btn-secondary:hover {
  background-color: #f5f5f5;
}

/* Hero Section */
.hero-section {

  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), 
              url('/assets/hero-bg.jpg');  /* Updated path */
  background-size: cover;
  background-position: center;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  border-radius: 8px;
  margin: 20px 0 40px;
}

.hero-content h1 {
  font-size: 48px;
  margin-bottom: 20px;
}

.subtitle {
  font-size: 20px;
  margin-bottom: 30px;
}

/* Categories Section */
.categories-section {
  margin: 60px 0;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.category-card {
  background: white;
  border-radius: 8px;
  padding: 25px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.category-card:hover {
  transform: translateY(-5px);
}

.category-card mat-icon {
  font-size: 40px;
  width: 40px;
  height: 40px;
  color: #4CAF50;
  margin-bottom: 15px;
}

.category-card h3 {
  margin-bottom: 10px;
  font-size: 18px;
}

.category-card p {
  color: #666;
  font-size: 14px;
}

/* Projects Section */
.projects-section {
  margin: 60px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.see-all {
  color: #4CAF50;
  text-decoration: none;
  font-weight: 500;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.project-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.project-card:hover {
  transform: translateY(-5px);
}

.project-image {
  height: 200px;
  background-size: cover;
  background-position: center;
    transition: transform 0.3s ease-in-out;
  transform-origin: center;
}
.project-card:hover .project-image {
  transform: scale(1.1);
}

.project-details {
  padding: 20px;
}

.project-category {
  color: #4CAF50;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
}

.project-description {
  color: #666;
  margin: 15px 0;
  font-size: 14px;
  line-height: 1.5;
}

.progress-container {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  margin: 15px 0;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: #4CAF50;
  border-radius: 3px;
}

.project-stats {
  display: flex;
  justify-content: space-between;
  margin: 15px 0;
  font-size: 14px;
  color: #666;
}

.amount {
  font-weight: 700;
  color: #333;
}

.days {
  font-weight: 700;
  color: #333;
}

.btn-support {
  width: 100%;
  padding: 10px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-support:hover {
  background-color: #3e8e41;
}

/* Success Stories */
.success-section {
  margin: 60px 0;
}

.success-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

.success-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.success-image {
  height: 150px;
  background-size: cover;
  background-position: center;
}

.success-details {
  padding: 20px;
}

.success-details h3 {
  margin-bottom: 15px;
}

.success-stats {
  display: flex;
  justify-content: space-between;
}

.success-stats div {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
}

.success-stats mat-icon {
  font-size: 18px;
  margin-right: 5px;
  color: #4CAF50;
}

/* CTA Section */
.cta-section {
  background-color: #f9f9f9;
  padding: 60px 0;
  text-align: center;
  border-radius: 8px;
  margin: 60px 0;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-content h2 {
  margin-bottom: 20px;
}

.cta-content p {
  margin-bottom: 30px;
  color: #666;
  font-size: 16px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 36px;
  }
  
  .subtitle {
    font-size: 18px;
  }
  
  .cta-buttons {
    display: flex;
    flex-direction: column;
  }
  
  .btn-secondary {
    margin-left: 0;
    margin-top: 15px;
  }
  
  .categories-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .hero-section {
    height: 400px;
  }
  
  .categories-grid {
    grid-template-columns: 1fr;
  }
}
/* Add this at the top if using material icons without Angular Material */
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
}
