<div class="project-management-container">
  <div class="header">
    <h2>Project Management</h2>
<div class="search-bar">
  <mat-form-field appearance="outline">
    <mat-label>Search projects</mat-label>
    <input matInput 
           [(ngModel)]="searchQuery" 
           (keyup)="applyFilter($event)"
           placeholder="Search...">
    <button mat-icon-button matSuffix (click)="onSearch()">
      <mat-icon>search</mat-icon>
    </button>
  </mat-form-field>
</div>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="50"></mat-spinner>
  </div>

  <div *ngIf="!isLoading" class="table-container">
    <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">
      <!-- ID Column -->
      <ng-container matColumnDef="projectId">
        <th mat-header-cell *matHeaderCellDef>ID</th>
        <td mat-cell *matCellDef="let project">{{ project.projectId }}</td>
      </ng-container>

      <!-- Title Column -->
      <ng-container matColumnDef="title">
        <th mat-header-cell *matHeaderCellDef>Title</th>
        <td mat-cell *matCellDef="let project">
          <div class="project-title">
            <img [src]="project.coverImage || 'assets/default-project.png'" alt="Project" class="project-image">
            <span>{{ project.title }}</span>
          </div>
        </td>
      </ng-container>

      <!-- Creator Column -->
      <ng-container matColumnDef="creator">
        <th mat-header-cell *matHeaderCellDef>Creator</th>
        <td mat-cell *matCellDef="let project">
          <div class="creator-info">
           <img [src]="project.creator?.profilePicture || 'assets/default-avatar.jpg'" alt="Creator" class="creator-avatar">
            <span>{{ project.userId }} {{ project.lastName }}</span>
          </div>
        </td>
      </ng-container>

      <!-- Category Column -->
      <ng-container matColumnDef="category">
        <th mat-header-cell *matHeaderCellDef>Category</th>
        <td mat-cell *matCellDef="let project">
          <span class="category-badge">{{ getcatname(project.categoryId) }}</span>
        </td>
      </ng-container>

      <!-- Funding Column -->
      <ng-container matColumnDef="funding">
        <th mat-header-cell *matHeaderCellDef>Funding</th>
        <td mat-cell *matCellDef="let project">
          <div class="funding-progress">
            <span class="amount">{{ project.amountRaised | currency }} of {{ project.fundingGoal | currency }}</span>
            <mat-progress-bar 
              mode="determinate" 
              [value]="getProgressPercentage(project)">
            </mat-progress-bar>
            <span class="percentage">{{ getProgressPercentage(project) | number:'1.0-0' }}%</span>
          </div>
        </td>
      </ng-container>

      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef>Status</th>
        <td mat-cell *matCellDef="let project">
          <mat-form-field appearance="outline">
            <mat-select [value]="project.status" (selectionChange)="updateProjectStatus(project, $event.value)">
              <mat-option value="Draft">Draft</mat-option>
              <mat-option value="Pending">Pending</mat-option>
              <mat-option value="Approved">Approved</mat-option>
              <mat-option value="Rejected">Rejected</mat-option>
              <mat-option value="Active">Active</mat-option>
              <mat-option value="Successful">Successful</mat-option>
              <mat-option value="Failed">Failed</mat-option>
            </mat-select>
          </mat-form-field>
        </td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let project">
          <button mat-icon-button color="primary" (click)="openEditDialog(project)">
            <mat-icon>edit</mat-icon>
          </button>
       <button mat-icon-button color="warn" (click)="confirmDelete(project)">
            <mat-icon>delete</mat-icon>
          </button>
        </td>
      </ng-container>

<tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
<tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>

   <mat-paginator
    [length]="dataSource.data.length"
    [pageSize]="10"
    [pageSizeOptions]="[5, 10, 25, 100]"
    showFirstLastButtons
    aria-label="Select page of projects">
  </mat-paginator>
  </div>
</div>