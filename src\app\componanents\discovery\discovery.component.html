<div class="discovery-container">
  <div class="discovery-header">
    <h1>Découvrir les Projets</h1>
    <p>Trouvez des projets inspirants à soutenir</p>
  </div>

  <div class="discovery-content">
    <!-- Filters Sidebar -->
<div class="filters-bar">
      <form [formGroup]="filtersForm" class="filters-form-horizontal">
        <div class="filter-group">
          <label for="search">Rechercher</label>
          <input class="search-input" type="text" formControlName="search" placeholder="Rechercher des projets...">
        </div>
        <div class="filter-group">
          <label for="">Catégorie</label>
          <select formControlName="category">
            <option value="">Toutes les Catégories</option>
            <option *ngFor="let cat of categories" [value]="cat.id">{{cat.name}}</option>
          </select>
        </div>
        <div class="filter-group">
          <label for="">Type de Financement</label>
          <select formControlName="fundingType">
            <option value="">Tous les Types</option>
            <option value="Donation">Don</option>
            <option value="Reward">Basé sur les récompenses</option>
            <option value="Equity">Participation</option>
            <option value="Loan">Prêt</option>
          </select>
        </div>
          <div class="filter-group">
            <label for="">Plage d'Objectif de Financement</label>
            <div class="range-inputs">
              <input
                type="number"
                formControlName="minGoal"
                placeholder="Montant Min"
                min="0">
              <span>à</span>
              <input
                type="number"
                formControlName="maxGoal"
                placeholder="Montant Max"
                min="0">
            </div>
          </div>
        <div class="filter-group">
          <label for="" >Trier par</label>
          <select formControlName="sort">
            <option *ngFor="let option of sortOptions" [value]="option.value">{{option.label}}</option>
          </select>
        </div>
        <button type="button" class="reset-btn" (click)="resetFilters()">Réinitialiser les Filtres</button>
      </form>
    </div>

    <!-- Projects Grid -->
    <main class="projects-grid-container">
      <div class="sort-controls">
        <span>{{filteredProjects.length}} projets trouvés</span>
      </div>

      <div *ngIf="isLoading" class="loading-spinner">
        <mat-spinner diameter="50"></mat-spinner>
      </div>

      <div *ngIf="!isLoading && filteredProjects.length === 0" class="no-results">
        <mat-icon>search_off</mat-icon>
        <h3>Aucun projet trouvé</h3>
        <p>Essayez d'ajuster vos filtres de recherche</p>
      </div>

      <div class="projects-grid">
        <div onkeydown="" *ngFor="let project of paginatedProjects" class="project-card" (click)="viewProject(project.projectId)">
          <div class="project-image" [style.backgroundImage]="'url(' + project.coverImage + ')'">
            <div class="project-category">
          <mat-icon>{{getCategoryById(project.categoryId)?.icon}}</mat-icon>
<span>{{getCategoryById(project.categoryId)?.name}}</span>

            </div>
          </div>
          
          <div class="project-details">
            <h3>{{project.title}}</h3>
            <p class="project-description">{{project.shortDescription | truncate:100}}</p>
            
            <div class="project-progress">
              <div class="progress-bar">
                <div class="progress-fill" [style.width]="progressPercentage(project) + '%'"></div>
              </div>
              <div class="progress-stats">
                <span class="percentage">{{progressPercentage(project) | number:'1.0-0'}}%</span>
                <span class="amount">{{project.amountRaised | currency}} collectés sur {{project.fundingGoal | currency}}</span>
              </div>
            </div>

            <div class="project-meta">
              <div class="views">
                <mat-icon>visibility</mat-icon>
                <span>{{project.viewsCount}} vues</span>
              </div>
              <div class="days-left">
                <mat-icon>schedule</mat-icon>
                <span>{{daysLeft(project)}} jours restants</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div *ngIf="filteredProjects.length > itemsPerPage" class="pagination">
        <button [disabled]="currentPage === 1" (click)="currentPage = currentPage - 1">
          <mat-icon>chevron_left</mat-icon>
        </button>
        
        <span onkeydown="" *ngFor="let page of getNumberArray(totalPages)" 
              [class.active]="page === currentPage"
              (click)="currentPage = page">
          {{page}}
        </span>
        
        <button [disabled]="currentPage === totalPages" (click)="currentPage = currentPage + 1">
          <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
    </main>
  </div>
</div>