<div class="start-project-container">
    <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <div *ngIf="!isLoading && hasExistingProject" class="existing-project-message">
    <mat-icon>warning</mat-icon>
    <h2>Project Limit Reached</h2>
    <p>You already have an active project. Only one project is allowed per user at a time.</p>
    <button mat-raised-button color="primary" routerLink="/discover">Browse Projects</button>
  </div>
  <div *ngIf="!isLoading && !hasExistingProject" class="project-form-container">
    <h1>Start Your Project</h1>
    <p class="subtitle">Bring your creative idea to life with the support of our community</p>
    
    <form [formGroup]="projectForm" (ngSubmit)="onSubmit()">
      <!-- Basic Information -->
      <div class="form-section">
        <h2>Basic Information</h2>
        <div class="form-group">
          <label for="title">Project Title*</label>
          <input type="text" id="title" formControlName="title" placeholder="What's your project called?">
          <div *ngIf="projectForm.get('title')?.invalid && projectForm.get('title')?.touched" class="error-message">
            Title is required and must be at least 5 characters
          </div>
        </div>
        <div class="form-row">
          <div class="form-group">
            <label for="category">Category*</label>
            <select id="category" formControlName="categoryId">
              <option value="">Select a category</option>
              <option *ngFor="let cat of categories" [value]="cat.id">{{cat.name}}</option>
            </select>
            <div *ngIf="projectForm.get('categoryId')?.invalid && projectForm.get('categoryId')?.touched" class="error-message">
  Please select a category
</div>
          </div>
          
          <div class="form-group">
            <label for="fundingType">Funding Type*</label>
            <select id="fundingType" formControlName="fundingType">
              <option value="">Select funding type</option>
              <option *ngFor="let type of fundingTypes" [value]="type.name">{{type.name}}</option>
            </select>
            <div *ngIf="projectForm.get('fundingType')?.invalid && projectForm.get('fundingType')?.touched" class="error-message">
              Please select a funding type
            </div>
          </div>
        </div>
      </div>
      
      <!-- Project Details -->
      <div class="form-section">
        <h2>Project Details</h2>
        <div class="form-group">
          <label for="description">Project Description*</label>
          <textarea id="description" formControlName="description" rows="6" 
                    placeholder="Tell the world about your project (at least 50 characters)..."></textarea>
          <div *ngIf="projectForm.get('description')?.invalid && projectForm.get('description')?.touched" class="error-message">
            Description must be at least 50 characters
          </div>
          <div class="character-count">
            {{ projectForm.get('description')?.value?.length || 0 }}/50
          </div>
        </div>

        <div class="form-group">
          <label for="shortDescription">Short Description*</label>
          <textarea id="shortDescription" formControlName="shortDescription" rows="2" 
                    placeholder="A brief summary of your project (max 100 characters)" [readOnly]="true"></textarea>
          <div class="character-count">
            {{ projectForm.get('shortDescription')?.value?.length || 0 }}/100
          </div>
        </div>

        <div class="form-group">
          <label for="risksChallenges">Risks & Challenges</label>
          <textarea id="risksChallenges" formControlName="risksChallenges" rows="3" 
                    placeholder="What challenges might you face and how will you overcome them?"></textarea>
        </div>
      </div>
      
      <!-- Funding Information -->
      <div class="form-section">
        <h2>Funding Information</h2>
        <div class="form-row">
          <div class="form-group">
            <label for="fundingGoal">Funding Goal (USD)*</label>
            <input type="number" id="fundingGoal" formControlName="fundingGoal" placeholder="How much do you need?">
            <div *ngIf="projectForm.get('fundingGoal')?.invalid && projectForm.get('fundingGoal')?.touched" class="error-message">
              Minimum funding goal is $100
            </div>
          </div>
          
          <div class="form-group">
            <label for="endDate">Campaign End Date*</label>
            <input type="date" id="endDate" formControlName="endDate" [min]="getMinEndDate()">
            <div *ngIf="projectForm.get('endDate')?.invalid && projectForm.get('endDate')?.touched" class="error-message">
              Please select a valid end date (at least 30 days from now)
            </div>
          </div>
        </div>
      </div>
      
      <!-- Media -->
      <div class="form-section">
        <h2>Media</h2>
        <div class="form-row">
<div class="form-group">
  <label for="coverImage">Cover Image*</label>
  <input type="file" id="coverImage" (change)="onFileChange($event)" accept="image/*" required>
  <small>Recommended size: 1200x675 pixels (Max 5MB)</small>
  
  <!-- Show preview -->
  <div *ngIf="previewImage" class="image-preview">
    <img [src]="previewImage" alt="Cover preview">
    <button type="button" (click)="removeImage()">Remove</button>
  </div>
  
  <div *ngIf="!previewImage" class="error-message">
    A cover image is required
  </div>
</div>
          
          <div class="form-group">
            <label for="videoUrl">Video URL (Optional)</label>
            <input type="url" id="videoUrl" formControlName="videoUrl" placeholder="https://youtube.com/yourvideo">
            <small>YouTube or Vimeo links work best</small>
          </div>
        </div>
      </div>
      <!-- Submit Section -->
      <div class="form-submit">
        <button type="submit" [disabled]="projectForm.invalid  && isSubmiting" class="submit-btn">Submit Project</button>
        <p class="form-note">By submitting, you agree to our Terms of Use and Privacy Policy</p>
      </div>
    </form>
  </div>
</div>