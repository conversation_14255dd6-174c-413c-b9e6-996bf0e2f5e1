:host {
  display: block;
  background-color: #f4f6f8;
  padding: 2rem 0;
}

.project-details-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  background-color: transparent;
  box-shadow: none;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

/* Hero Section */
.project-hero {
  height: 450px;
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 2rem;
  background-size: cover;
  background-position: center;
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.hero-overlay {
  background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
  padding: 2.5rem;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 0.5rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
  font-size: 1.25rem;
  font-weight: 400;
  margin: 0;
  max-width: 80%;
}

/* Main Content */
.project-main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  align-items: flex-start;
}

.project-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.info-card {
  border-radius: 12px;
}

.project-description, .project-risks {
  line-height: 1.7;
  font-size: 1.05rem;
  color: #333;
}

/* Sidebar */
.project-sidebar {
  position: sticky;
  top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.funding-card, .video-card {
  border-radius: 12px;
}

.funding-progress {
  text-align: left;
  margin-bottom: 1rem;
}

.amount-raised {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e88e5; /* A fresh blue */
  margin: 0;
}

.funding-goal {
  color: #555;
  font-size: 1rem;
}

.progress-bar {
  height: 10px;
  border-radius: 5px;
  margin-bottom: 1.5rem;
}

.funding-stats {
  display: flex;
  justify-content: space-between;
  text-align: center;
  margin-bottom: 1.5rem;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 600;
  display: block;
}

.stat-label {
  font-size: 0.9rem;
  color: #777;
}

.support-button {
  width: 100%;
  padding: 1.5rem 0;
  font-size: 1.1rem;
  font-weight: 500;
  line-height: 0;
  margin-bottom: 1.5rem;
  border-radius: 8px;
}

.mat-divider {
  margin-bottom: 1.5rem;
}

.project-meta {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #444;
  font-size: 0.95rem;
}

.meta-item mat-icon {
  color: #1e88e5;
}

/* Video */
.video-embed {
  position: relative;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
  height: 0;
  overflow: hidden;
  border-radius: 8px;
}

.video-embed iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

/* Responsive */
@media (max-width: 992px) {
  .project-main-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  :host {
    padding: 1rem 0;
  }
  .project-hero {
    height: 350px;
  }
  .hero-title {
    font-size: 2.25rem;
  }
  .hero-subtitle {
    font-size: 1.1rem;
    max-width: 100%;
  }
  .hero-overlay {
    padding: 1.5rem;
  }
}