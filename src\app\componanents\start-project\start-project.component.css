.start-project-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.project-form-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #7f8c8d;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.form-section {
  margin-bottom: 2.5rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #ecf0f1;
}

h2 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2c3e50;
}

input[type="text"],
input[type="number"],
input[type="url"],
input[type="date"],
textarea,
select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

input[type="file"] {
  padding: 0.5rem 0;
}

textarea {
  min-height: 120px;
  resize: vertical;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: #4CAF50;
}

.form-row {
  display: flex;
  gap: 1.5rem;
}

.form-row .form-group {
  flex: 1;
}

.error-message {
  color: #e74c3c;
  font-size: 0.85rem;
  margin-top: 0.5rem;
}

/* Rewards Section */
.rewards-container {
  margin-top: 1.5rem;
}

.reward-card {
  background: #f9f9f9;
  border-radius: 6px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.remove-reward {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #e74c3c;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.remove-reward:hover {
  background: #f5f5f5;
}

.add-reward {
  background: #ecf0f1;
  border: 1px dashed #bdc3c7;
  color: #7f8c8d;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s;
}

.add-reward:hover {
  background: #e0e0e0;
}

/* Submit Section */
.form-submit {
  text-align: center;
  margin-top: 2rem;
}

.submit-btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 500;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.submit-btn:hover {
  background: #3e8e41;
}

.submit-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.form-note {
  color: #7f8c8c;
  font-size: 0.85rem;
  margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .project-form-container {
    padding: 1.5rem;
  }
}


.upload-status {
  margin: 10px 0;
  color: #666;
}

.image-preview {
  margin-top: 10px;
  img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 4px;
  }
  button {
    display: block;
    margin-top: 5px;
    background: #f5f5f5;
    border: 1px solid #ddd;
    padding: 3px 8px;
    border-radius: 3px;
    cursor: pointer;
    &:hover {
      background: #eee;
    }
  }
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.existing-project-message {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.existing-project-message mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  color: #f44336;
  margin-bottom: 1rem;
}

.existing-project-message h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.existing-project-message p {
  color: #7f8c8d;
  margin-bottom: 2rem;
}

.existing-project-message button {
  padding: 0.5rem 2rem;
}