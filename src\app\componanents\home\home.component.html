<div class="home-container">
  <!-- Hero Section -->
   <section class="hero-section" [ngStyle]="heroBackground">
    <div class="hero-content">
      <h1>Donnez vie aux projets créatifs</h1>
      <p class="subtitle">La plus grande plateforme de financement au monde pour les projets créatifs</p>
      <div class="cta-buttons">
       <button class="btn-primary" (click)="navigateTo('start-project')">Créer un Projet</button>
        <button class="btn-secondary" (click)="navigateTo('discover')">Parcourir les Projets</button>
      </div>
    </div>
  </section>

  <!-- Categories Section -->
  <section class="categories-section">
    <h2>Catégories de Projets</h2>
    <div class="categories-grid">
      <div *ngFor="let category of featuredCategories" class="category-card">
        <span class="material-icons">{{category.icon}}</span>
        <h3>{{category.name}}</h3>
        <p>{{category.count}} projets</p>
      </div>
    </div>
  </section>

  <!-- Trending Projects -->
  <section class="projects-section">
    <div class="section-header">
      <h2>Projets Tendance</h2>
      <a href="/discover" class="see-all">Voir tout</a>
    </div>
    <div class="projects-grid">
      <div *ngFor="let project of trendingProjects" class="project-card">
        <div class="project-image" [style.backgroundImage]="'url(' + project.imageUrl + ')'"></div>
        <div class="project-details">
          <div class="project-category">{{project.category}}</div>
          <h3>{{project.title}}</h3>
          <p class="project-description">{{project.description}}</p>
          
          <div class="progress-container">
            <div class="progress-bar" [style.width]="project.progress + '%'"></div>
          </div>
          
          <div class="project-stats">
            <div>
              <span class="amount">{{project.amountRaised | currency}}</span>
              <span>collectés sur {{project.goal | currency}} objectif</span>
            </div>
            <div>
              <span class="days">{{project.daysLeft}}</span>
              <span>jours restants</span>
            </div>
          </div>

          <button class="btn-support">Soutenir</button>
        </div>
      </div>
    </div>
  </section>

  <!-- Success Stories -->
  <section class="success-section">
    <h2>Histoires de Succès</h2>
    <div class="success-grid">
      <div *ngFor="let story of successStories" class="success-card">
        <div class="success-image" [style.backgroundImage]="'url(' + story.imageUrl + ')'"></div>
        <div class="success-details">
          <h3>{{story.title}}</h3>
          <div class="success-stats">
            <div>
              <mat-icon>attach_money</mat-icon>
              <span>{{story.raised | currency}} collectés</span>
            </div>
            <div>
              <mat-icon>people</mat-icon>
              <span>{{story.backers}} contributeurs</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Call to Action -->
  <section class="cta-section">
    <div class="cta-content">
      <h2>Prêt à donner vie à votre idée ?</h2>
      <p>Rejoignez des milliers de créateurs qui ont concrétisé leurs rêves grâce à notre plateforme.</p>
      <button class="btn-primary" (click)="navigateTo('start-project')">Créer Votre Projet</button>
    </div>
  </section>
</div>