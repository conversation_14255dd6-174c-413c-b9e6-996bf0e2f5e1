<div class="login-container">
  <div class="logo-section">
    <h1 class="logo-text">Crowd<span>Fund</span></h1>
    <p class="subtitle">Bon retour !</p>
  </div>

  <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
    <div class="form-group">
      <label for="email">Email</label>
      <div class="input-with-icon">
        <mat-icon>email</mat-icon>
        <input
          type="email"
          id="email"
          formControlName="email"
          placeholder="Entrez votre email">
      </div>
      <div *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched"
           class="error-message">
        Veuillez entrer un email valide
      </div>
    </div>

    <div class="form-group">
      <label for="password">Mot de passe</label>
      <div class="input-with-icon">
        <mat-icon>lock</mat-icon>
        <input
          [type]="hidePassword ? 'password' : 'text'"
          id="password"
          formControlName="password"
          placeholder="Entrez votre mot de passe">
        <mat-icon class="password-toggle" (click)="hidePassword = !hidePassword">
          {{hidePassword ? 'visibility_off' : 'visibility'}}
        </mat-icon>
      </div>
      <div *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched"
           class="error-message">
        Le mot de passe doit contenir au moins 6 caractères
      </div>
    </div>

    <div class="form-options">
      <div class="remember-me">
        <mat-checkbox formControlName="rememberMe" color="primary">
          Se souvenir de moi
        </mat-checkbox>
      </div>
      <a routerLink="/forgot-password">Mot de passe oublié ?</a>
    </div>

    <button type="submit" class="submit-btn" [disabled]="loginForm.invalid || isLoading">
      <mat-icon *ngIf="!isLoading">login</mat-icon>
      <span *ngIf="!isLoading">Connexion</span>
      <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
    </button>

    <div *ngIf="errorMessage" class="error-message server-error">
      {{ errorMessage }}
    </div>
  </form>
</div>