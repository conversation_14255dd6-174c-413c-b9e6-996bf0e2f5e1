<form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
  <div class="form-group">
    <label for="username">Prénom</label>
    <div class="input-with-icon">
      <mat-icon>person</mat-icon>
      <input
        type="text"
        id="username"
        formControlName="username"
        placeholder="Entrez votre prénom">
    </div>
    <div *ngIf="registerForm.get('username')?.invalid && registerForm.get('username')?.touched"
         class="error-message">
      Veuillez entrer votre prénom
    </div>
  </div>

    <div class="form-group">
    <label for="lastName">Nom de famille</label>
    <div class="input-with-icon">
      <mat-icon>person_outline</mat-icon>
      <input
        type="text"
        id="lastName"
        formControlName="lastName"
        placeholder="Entrez votre nom de famille">
    </div>
    <div *ngIf="registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched"
         class="error-message">
      Veuillez entrer votre nom de famille
    </div>
  </div>

  <div class="form-group">
    <label for="email">Email</label>
    <div class="input-with-icon">
      <mat-icon>email</mat-icon>
      <input
        type="email"
        id="email"
        formControlName="email"
        placeholder="Entrez votre email">
    </div>
    <div *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched"
         class="error-message">
      Veuillez entrer un email valide
    </div>
  </div>

   <div class="form-group">
    <label for="phoneNumber">Numéro de téléphone</label>
    <div class="input-with-icon">
      <mat-icon>phone</mat-icon>
      <input
        type="tel"
        id="phoneNumber"
        formControlName="phoneNumber"
        placeholder="Entrez votre numéro de téléphone">
    </div>
    <div *ngIf="registerForm.get('phoneNumber')?.invalid && registerForm.get('phoneNumber')?.touched"
         class="error-message">
      Veuillez entrer un numéro de téléphone valide à 10 chiffres
    </div>
  </div>

  <div class="form-group">
    <label for="password">Mot de passe</label>
    <input
      type="password"
      id="password"
      formControlName="password"
      placeholder="Entrez votre mot de passe">
    <div *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched"
         class="error-message">
      Le mot de passe doit contenir au moins 8 caractères
    </div>
  </div>

  <div class="form-group">
    <label for="confirmPassword">Confirmer le mot de passe</label>
    <input
      type="password"
      id="confirmPassword"
      formControlName="confirmPassword"
      placeholder="Confirmez votre mot de passe">
    <div *ngIf="registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched"
         class="error-message">
      Les mots de passe doivent correspondre
    </div>
  </div>

  <div class="form-group">
    <label for="userType">Je veux</label>
    <select id="userType" formControlName="userType">
      <option value="Backer">Soutenir des projets</option>
      <option value="ProjectOwner">Créer des projets</option>
    </select>
  </div>

  <div class="form-check">
    <input type="checkbox" id="terms" formControlName="acceptTerms">
    <label for="terms">
      J'accepte les <a href="/terms" target="_blank">Conditions d'utilisation</a> et la
      <a href="/privacy" target="_blank">Politique de confidentialité</a>
    </label>
    <div *ngIf="registerForm.get('acceptTerms')?.invalid && registerForm.get('acceptTerms')?.touched"
         class="error-message">
      Vous devez accepter les conditions
    </div>
  </div>

  <button type="submit" class="submit-btn" [disabled]="registerForm.invalid || isLoading">
    <span *ngIf="!isLoading">Créer un compte</span>
    <span *ngIf="isLoading">Création du compte...</span>
  </button>

  <div *ngIf="errorMessage" class="error-message server-error">
    {{ errorMessage }}
  </div>
</form>