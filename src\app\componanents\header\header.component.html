<header class="app-header">
  <div class="header-container">
    <!-- Logo -->
    <div onkeydown="" class="logo" (click)="navigateTo('')">
          <h1 class="logo-text">Crowd<span>Fund</span></h1>

    </div>

    <!-- Desktop Navigation -->
    <nav class="desktop-nav">
      <ul>
        <li><a (click)="navigateTo('')">Accueil</a></li>
        <li><a (click)="navigateTo('discover')">Découvrir</a></li>
        <li><a *ngIf="!isAdmin() && !isBacker()" (click)="navigateTo('start-project')">Créer un Projet</a></li>
        <li><a (click)="navigateTo('about')">À Propos</a></li>
      </ul>
    </nav>

    <!-- User Actions -->
    <div class="user-actions">
      <div *ngIf="isLoggedIn" class="logged-in">
        <button class="btn-notifications">
          <i class="material-icons">notifications</i>
        </button>
        <div class="user-dropdown">
          <button class="user-avatar" (click)="toggleDropdown()">
            <img src="assets/default-avatar.jpg" alt="User">
            <i class="material-icons dropdown-icon">arrow_drop_down</i>
          </button>
  <div class="dropdown-menu" [class.show]="showDropdown">

  <a (click)="navigateTo('settings')">
    <mat-icon>settings</mat-icon>
    <span>Paramètres</span>
  </a>
  <a *ngIf="isAdmin()" (click)="navigateTo('/dashboard/welcome')">
    <mat-icon>dashboard</mat-icon>
    <span>Tableau de bord Admin</span>
  </a>
  <a *ngIf="isCreator()" (click)="navigateTop('/project/')">
    <mat-icon>edit</mat-icon>
    <span>Mon Projet</span>
  </a>
  <a (click)="logout()">
    <mat-icon>logout</mat-icon>
    <span>Déconnexion</span>
  </a>
</div>
        </div>
      </div>
      <div *ngIf="!isLoggedIn" class="logged-out">
        <button class="user-avatar" (click)="toggleDropdown()">
          <i class="material-icons">account_circle</i>
          <i class="material-icons dropdown-icon">arrow_drop_down</i>
        </button>
   <div class="dropdown-menu" [class.show]="showDropdown">
  <a (click)="navigateTo('login')">
    <mat-icon>login</mat-icon>
    <span>Connexion</span>
  </a>
  <a *ngIf="isAdmin()" (click)="navigateTo('/dashboard')">
    <mat-icon>dashboard</mat-icon>
    <span>Tableau de bord Admin</span>
  </a>
  <a *ngIf="isCreator()" (click)="navigateTo('/project/1')">
    <mat-icon>edit</mat-icon>
    <span>Mon Projet</span>
  </a>
</div>
      </div>
    </div>

    <!-- Mobile Menu Toggle -->
    <button class="mobile-menu-toggle" (click)="toggleMobileMenu()">
      <i class="material-icons">{{ showMobileMenu ? 'close' : 'menu' }}</i>
    </button>
  </div>

  <!-- Mobile Navigation -->
  <div class="mobile-nav" [class.active]="showMobileMenu">
    <ul>
      <li><a (click)="navigateTo('')">Accueil</a></li>
      <li><a (click)="navigateTo('discover')">Découvrir</a></li>
      <li><a *ngIf="!isAdmin()" (click)="navigateTo('start-project')">Créer un Projet</a></li>
      <li><a (click)="navigateTo('about')">À Propos</a></li>
      <li *ngIf="!isLoggedIn">
        <a (click)="navigateTo('login')">Connexion</a>
      </li>
      <li *ngIf="isLoggedIn">
        <a (click)="logout()">Déconnexion</a>
      </li>
    </ul>
  </div>
</header>