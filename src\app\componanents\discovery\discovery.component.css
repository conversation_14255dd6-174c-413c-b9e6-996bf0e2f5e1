.discovery-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background: #f8f9fa;
}

.discovery-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.discovery-header::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: #4CAF50;
  border-radius: 2px;
}

.discovery-header h1 {
  font-size: 2.8rem;
  color: #1a237e;
  margin-bottom: 1rem;
  font-weight: 700;
}

.discovery-header p {
  color: #546e7a;
  font-size: 1.2rem;
}

/* Filters Bar Styling */
.filters-bar {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem;
}

.filters-form-horizontal {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  align-items: end;
}

.filter-group label {
  color: #37474f;
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

.filter-group input,
.filter-group select {
  width: 100%;
  padding: 0.8rem 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #4CAF50;
  outline: none;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.reset-btn {
  background: #ff5252;
  color: white;
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 130px;
  height: 47px;
}

.reset-btn:hover {
  background: #ff1744;
  transform: translateY(-2px);
}

/* Projects Grid Styling */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  padding: 1rem 0;
}

.project-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.project-image {
  height: 200px;
  position: relative;
  overflow: hidden;
  background-size: contain;  /* Changed from 'cover' to 'contain' */
  background-position: center;
  background-repeat: no-repeat;  /* Added to prevent image repetition */
  background-color: #f5f5f5;  /* Added light background for empty spaces */
  transition: opacity 0.3s ease;

}
.search-input{
  max-width: 180px;
}

.project-image::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60%;
  background: linear-gradient(to top, rgba(0,0,0,0.6), transparent);
  pointer-events: none; /* Ensures the gradient doesn't interfere with clicks */
}

/* Optional: Add a placeholder while image loads */
.project-image:empty {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { background-color: #f5f5f5; }
  50% { background-color: #e0e0e0; }
  100% { background-color: #f5f5f5; }
}

.project-category {
  position: absolute;
  top: 1rem;
  left: 1rem;
  z-index: 1;
  background: rgba(255, 255, 255, 0.95);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.project-category mat-icon {
  color: #4CAF50;
}

.project-details {
  padding: 1.5rem;
}

.project-details h3 {
  font-size: 1.25rem;
  color: #1a237e;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.project-description {
  color: #546e7a;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.project-progress {
  margin-bottom: 1.5rem;
}

.progress-bar {
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.8rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(45deg, #4CAF50, #81c784);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.percentage {
  font-weight: 600;
  color: #4CAF50;
  font-size: 1.1rem;
}

.amount {
  color: #546e7a;
  font-size: 0.9rem;
}

.project-meta {
  display: flex;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px solid #e0e0e0;
}

.project-meta div {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #546e7a;
}

.project-meta mat-icon {
  font-size: 1.2rem;
  color: #4CAF50;
}

/* Pagination Styling */
.pagination {
  margin-top: 3rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.pagination button,
.pagination span {
  min-width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination button {
  background: white;
  border: 2px solid #e0e0e0;
  color: #546e7a;
}

.pagination button:hover:not(:disabled) {
  background: #4CAF50;
  border-color: #4CAF50;
  color: white;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination span {
  font-weight: 500;
  color: #546e7a;
}

.pagination span.active {
  background: #4CAF50;
  color: white;
}

/* Loading and No Results States */
.loading-spinner {
  padding: 4rem 0;
  display: flex;
  justify-content: center;
}

.no-results {
  text-align: center;
  padding: 4rem 0;
}

.no-results mat-icon {
  font-size: 4rem;
  color: #b0bec5;
  margin-bottom: 1rem;
}

.no-results h3 {
  color: #1a237e;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.no-results p {
  color: #546e7a;
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.range-inputs input {
  flex: 1;
  width: 100%;
  padding: 0.8rem 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.range-inputs span {
  color: #546e7a;
  font-weight: 500;
  padding: 0 5px;
}

.range-inputs input:focus {
  border-color: #4CAF50;
  outline: none;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.range-inputs input::placeholder {
  color: #9e9e9e;
}

/* Add specific styles for number inputs */
.range-inputs input[type="number"] {
  -moz-appearance: textfield; /* Firefox */
}

.range-inputs input[type="number"]::-webkit-outer-spin-button,
.range-inputs input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .discovery-container {
    padding: 1rem;
  }

  .discovery-header h1 {
    font-size: 2rem;
  }

  .filters-form-horizontal {
    grid-template-columns: 1fr;
  }

  .projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .range-inputs {
    flex-direction: row;
    gap: 8px;
  }
  
  .range-inputs input {
    padding: 0.7rem;
    font-size: 0.9rem;
  }
}