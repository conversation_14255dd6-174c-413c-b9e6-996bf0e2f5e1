.admin-container {
  font-family: 'Roboto', sans-serif;
  color: #333;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), #4CAF50;
  background-size: cover;
  background-position: center;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  border-radius: 8px;
  margin: 20px 0 40px;
}

.hero-content h1 {
  font-size: 48px;
  margin-bottom: 20px;
}

.subtitle {
  font-size: 20px;
  margin-bottom: 30px;
}

/* Stats Section */
.stats-section {
  margin: 40px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 25px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card mat-icon {
  font-size: 40px;
  width: 40px;
  height: 40px;
  color: #4CAF50;
  margin-right: 20px;
}

.stat-details h3 {
  margin: 0;
  font-size: 16px;
  color: #666;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 5px 0 0;
}

/* Activity Section */
.activity-section, .users-section {
  margin: 60px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.see-all {
  color: #4CAF50;
  text-decoration: none;
  font-weight: 500;
}

.activity-grid, .users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.activity-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.activity-card:hover {
  transform: translateY(-5px);
}

.status-chip {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  background: #4CAF50;
  color: white;
  margin: 10px 0;
}

.status-chip.pending {
  background: #FFA000;
}

.category-tag {
  color: #4CAF50;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
}

/* User Cards */
.user-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.user-card:hover {
  transform: translateY(-5px);
}

.user-avatar {
  margin-right: 20px;
}

.user-avatar mat-icon {
  font-size: 40px;
  width: 40px;
  height: 40px;
  color: #4CAF50;
}

.user-details h3 {
  margin: 0;
  font-size: 18px;
}

.user-type {
  color: #4CAF50;
  font-size: 14px;
  margin: 5px 0;
}

.join-date, .project-count {
  color: #666;
  font-size: 14px;
  margin: 5px 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 36px;
  }
  
  .subtitle {
    font-size: 18px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .hero-section {
    height: 200px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .activity-grid, .users-grid {
    grid-template-columns: 1fr;
  }
}