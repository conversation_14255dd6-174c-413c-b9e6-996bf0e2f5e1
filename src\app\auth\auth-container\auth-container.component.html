<div class="auth-container">
  <div class="auth-card">
    <div class="auth-tabs">
      <button
        [class.active]="activeTab === 'login'"
        (click)="activeTab = 'login'">
        Connexion
      </button>
      <button
        [class.active]="activeTab === 'register'"
        (click)="activeTab = 'register'">
        Inscription
      </button>
    </div>

    <div class="auth-content">
      <app-login *ngIf="activeTab === 'login'"></app-login>
      <app-register *ngIf="activeTab === 'register'"></app-register>
    </div>

    <div class="auth-footer">
      <div *ngIf="activeTab === 'login'">
        Vous n'avez pas de compte ? <a (click)="activeTab = 'register'">Inscrivez-vous ici</a>
      </div>
      <div *ngIf="activeTab === 'register'">
        Vous avez déjà un compte ? <a (click)="activeTab = 'login'">Connectez-vous ici</a>
      </div>
    </div>
  </div>
</div>